---
import { Image } from 'astro:assets';
import profileImage from '../../assets/profile.jpg';
---
<!-- SECTION: Hero -->

<section id="inicio" class="py-12 sm:py-16 lg:py-20">
    <div class="flex flex-col md:flex-row items-center gap-8 md:gap-12">
        <!-- Profile Image -->
        <div class="flex-shrink-0 order-1 md:order-2">
            <div class="relative profile-border">
                <Image
                    src={profileImage}
                    alt="Alexis Ocando - Desarrollador Full-Stack"
                    class="profile-image w-32 h-32 sm:w-40 sm:h-40 md:w-48 md:h-48 lg:w-56 lg:h-56 rounded-full object-cover border-4 border-slate-800 shadow-2xl relative z-10"
                    width={224}
                    height={224}
                    quality={90}
                    format="webp"
                    loading="eager"
                    fetchpriority="high"
                />
                <!-- Decorative ring -->
                <div class="absolute inset-0 rounded-full border-2 border-sky-400/30 animate-pulse z-20"></div>
                <!-- Status indicator -->
                <div class="absolute bottom-2 right-2 sm:bottom-3 sm:right-3 md:bottom-4 md:right-4 z-30">
                    <div class="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-green-400 rounded-full border-2 border-slate-900 animate-pulse shadow-lg">
                        <div class="w-full h-full bg-green-400 rounded-full animate-ping opacity-75"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="flex-1 text-center md:text-left order-2 md:order-1">
            <h1 class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-white leading-tight">
                Hola, soy <span class="block sm:inline">Alexis Ocando</span>
            </h1>
            <h2 class="mt-3 sm:mt-2 text-lg sm:text-xl md:text-2xl font-bold text-sky-400 leading-relaxed">
                Desarrollador Backend y de <span class="block sm:inline">Sistemas Embebidos</span>
            </h2>
            <p class="mt-4 sm:mt-6 max-w-2xl mx-auto md:mx-0 text-slate-400 text-sm sm:text-base leading-relaxed px-2 sm:px-0">
                Apasionado por construir soluciones de software robustas y eficientes, desde la lógica de servidor hasta el firmware que vive dentro del hardware.
            </p>
            <div class="mt-6 sm:mt-8 flex flex-col sm:flex-row justify-center md:justify-start items-center gap-3 sm:gap-4 px-4 sm:px-0">
                <a href="https://github.com/tomjod" target="_blank" class="w-full sm:w-auto flex items-center justify-center gap-2 bg-slate-800 hover:bg-slate-700 text-white font-semibold py-3 sm:py-2 px-6 sm:px-4 rounded-lg transition-colors">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                    </svg>
                    GitHub
                </a>
                <a href="https://linkedin.com/in/tomjod" target="_blank" class="w-full sm:w-auto flex items-center justify-center gap-2 bg-slate-800 hover:bg-slate-700 text-white font-semibold py-3 sm:py-2 px-6 sm:px-4 rounded-lg transition-colors">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                    </svg>
                    LinkedIn
                </a>
            </div>
        </div>
    </div>
</section>