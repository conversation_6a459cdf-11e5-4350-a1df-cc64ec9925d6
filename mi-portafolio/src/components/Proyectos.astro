<!-- SECTION: Projects -->
<section id="proyectos" class="py-16">
    <h2 class="text-3xl font-bold text-white mb-8">Proyectos Destacados</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- Project 1 -->
        <div class="bg-slate-800 rounded-lg p-6 hover:ring-2 ring-sky-400 transition-all">
            <h3 class="text-xl font-bold text-white">buicap-py</h3>
            <div class="mt-2 flex flex-wrap gap-2">
                <span class="text-xs font-semibold bg-sky-900/50 text-sky-300 py-1 px-2 rounded-full">Python</span>
                <span class="text-xs font-semibold bg-sky-900/50 text-sky-300 py-1 px-2 rounded-full">ctypes</span>
                <span class="text-xs font-semibold bg-sky-900/50 text-sky-300 py-1 px-2 rounded-full">DLL Integration</span>
                <span class="text-xs font-semibold bg-sky-900/50 text-sky-300 py-1 px-2 rounded-full">Hardware SDK</span>
            </div>
            <p class="mt-4 text-slate-400">Python bindings para interactuar con las funciones nativas DLL de Digital Check. Este wrapper proporciona una interfaz Pythónica para acceder de forma segura a la funcionalidad expuesta por el SDK DLL de Digital Check para escáneres de documentos.</p>
            <a href="https://github.com/tomjod/buicap-py" target="_blank" class="mt-4 inline-block text-sky-400 font-semibold hover:underline">Ver en GitHub &rarr;</a>
        </div>
        <!-- Project 2 -->
        <div class="bg-slate-800 rounded-lg p-6 hover:ring-2 ring-sky-400 transition-all">
            <h3 class="text-xl font-bold text-white">Smart Height Measure</h3>
            <div class="mt-2 flex flex-wrap gap-2">
                <span class="text-xs font-semibold bg-green-900/50 text-green-300 py-1 px-2 rounded-full">C++</span>
                <span class="text-xs font-semibold bg-green-900/50 text-green-300 py-1 px-2 rounded-full">Arduino</span>
                <span class="text-xs font-semibold bg-green-900/50 text-green-300 py-1 px-2 rounded-full">PlatformIO</span>
                <span class="text-xs font-semibold bg-green-900/50 text-green-300 py-1 px-2 rounded-full">HC-SR04</span>
            </div>
            <p class="mt-4 text-slate-400">Medidor de altura inteligente que utiliza sensor ultrasónico HC-SR04 y pantalla TFT ST7789. Proporciona mediciones precisas en tiempo real con filtro de mediana, interfaz visual atractiva y animaciones.</p>
            <a href="https://github.com/tomjod/Smart-Measure-Height-with-Arduino-UNO-and-Ultrasonic-Sensor-HC-SR04" target="_blank" class="mt-4 inline-block text-sky-400 font-semibold hover:underline">Ver en GitHub &rarr;</a>
        </div>
    </div>
</section>