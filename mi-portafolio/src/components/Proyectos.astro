<!-- SECTION: Projects -->
<section id="proyectos" class="py-12 sm:py-16">
    <h2 class="text-2xl sm:text-3xl font-bold text-white mb-6 sm:mb-8 text-center md:text-left">Proyectos Destacados</h2>
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8">
        <!-- Project 1 -->
        <div class="bg-slate-800 rounded-lg p-4 sm:p-6 hover:ring-2 ring-sky-400 transition-all">
            <h3 class="text-lg sm:text-xl font-bold text-white text-center lg:text-left">buicap-py</h3>
            <div class="mt-3 sm:mt-2 flex flex-wrap gap-2 justify-center lg:justify-start">
                <span class="text-xs font-semibold bg-sky-900/50 text-sky-300 py-1 px-2 rounded-full">Python</span>
                <span class="text-xs font-semibold bg-sky-900/50 text-sky-300 py-1 px-2 rounded-full">ctypes</span>
                <span class="text-xs font-semibold bg-sky-900/50 text-sky-300 py-1 px-2 rounded-full">DLL Integration</span>
                <span class="text-xs font-semibold bg-sky-900/50 text-sky-300 py-1 px-2 rounded-full">Hardware SDK</span>
            </div>
            <p class="mt-4 text-slate-400 text-sm sm:text-base leading-relaxed text-center lg:text-left">Python bindings para interactuar con las funciones nativas DLL de Digital Check. Este wrapper proporciona una interfaz Pythónica para acceder de forma segura a la funcionalidad expuesta por el SDK DLL de Digital Check para escáneres de documentos.</p>
            <div class="mt-4 text-center lg:text-left">
                <a href="https://github.com/tomjod/buicap-py" target="_blank" class="inline-block text-sky-400 font-semibold hover:underline text-sm sm:text-base">Ver en GitHub &rarr;</a>
            </div>
        </div>
        <!-- Project 2 -->
        <div class="bg-slate-800 rounded-lg p-4 sm:p-6 hover:ring-2 ring-sky-400 transition-all">
            <h3 class="text-lg sm:text-xl font-bold text-white text-center lg:text-left">Smart Height Measure</h3>
            <div class="mt-3 sm:mt-2 flex flex-wrap gap-2 justify-center lg:justify-start">
                <span class="text-xs font-semibold bg-green-900/50 text-green-300 py-1 px-2 rounded-full">C++</span>
                <span class="text-xs font-semibold bg-green-900/50 text-green-300 py-1 px-2 rounded-full">Arduino</span>
                <span class="text-xs font-semibold bg-green-900/50 text-green-300 py-1 px-2 rounded-full">PlatformIO</span>
                <span class="text-xs font-semibold bg-green-900/50 text-green-300 py-1 px-2 rounded-full">HC-SR04</span>
            </div>
            <p class="mt-4 text-slate-400 text-sm sm:text-base leading-relaxed text-center lg:text-left">Medidor de altura inteligente que utiliza sensor ultrasónico HC-SR04 y pantalla TFT ST7789. Proporciona mediciones precisas en tiempo real con filtro de mediana, interfaz visual atractiva y animaciones.</p>
            <div class="mt-4 text-center lg:text-left">
                <a href="https://github.com/tomjod/Smart-Measure-Height-with-Arduino-UNO-and-Ultrasonic-Sensor-HC-SR04" target="_blank" class="inline-block text-sky-400 font-semibold hover:underline text-sm sm:text-base">Ver en GitHub &rarr;</a>
            </div>
        </div>
    </div>
</section>