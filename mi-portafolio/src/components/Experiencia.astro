<!-- SECTION: Experience -->
<section id="experiencia" class="py-16">
    <h2 class="text-3xl font-bold text-white mb-8">Experiencia Profesional</h2>
    <div class="space-y-12">
        <!-- Job 1 -->
        <div class="flex flex-col md:flex-row gap-6">
            <p class="text-sky-400 font-semibold w-full md:w-1/4">2021 - Presente</p>
            <div class="w-full md:w-3/4">
                <h3 class="text-xl font-bold text-white">Desarrollador de Software Full-Stack | Mundos Virtuales SPA</h3>
                <p class="mt-2 text-slate-400">
                    Ingeniero full-stack especializado en el desarrollo completo de soluciones software-hardware. Lideré la modernización de un sistema transaccional desde una aplicación de escritorio (C# / Windows Forms) hacia una solución móvil nativa para terminales POS (PAX A8700).
                </p>
                <div class="mt-4">
                    <h4 class="text-lg font-semibold text-white mb-2">Logros principales:</h4>
                    <ul class="text-slate-400 space-y-2 list-disc list-inside">
                        <li>Desarrollé desde cero sistema completo de conteo de billetes con interfaz Windows Forms (C#) incluyendo autenticación, dashboard, estadísticas y pantalla de conteo principal</li>
                        <li>Integré hardware especializado implementando comunicación serial con validadora de billetes para procesamiento automático de transacciones</li>
                        <li>Diseñé y desarrollé placa de control personalizada con Arduino y KiCad para centralizar control de actuadores y monitoreo de sensores en tiempo real</li>
                        <li>Implementé sistema de control automatizado donde sensores activos disparan apertura de shutter via actuador, permitiendo flujo controlado de billetes</li>
                        <li>Arquitecté backend escalable con ASP.NET Core 8 y API RESTful para persistencia y gestión de datos transaccionales</li>
                        <li>Migré solución completa a Android nativo manteniendo toda la funcionalidad hardware-software en plataforma móvil</li>
                        <li>Automaticé soporte técnico regional con plataforma n8n integrando WhatsApp/Telegram para que técnicos consulten IA especializada con RAG sobre manuales Glory UWF</li>
                        <li>Modernicé aplicación legacy refactorizando código Python con PyQt6 para integración con contadora Glory UWF via red</li>
                    </ul>
                </div>
                <div class="mt-4">
                    <p class="text-sm text-sky-300"><strong>Stack técnico:</strong> C#, Kotlin, Python, PyQt6, Android, ASP.NET 8, PostgreSQL, Arduino, KiCad, Jetpack Compose, n8n, RAG, IA/LLM</p>
                </div>
            </div>
        </div>
        <!-- Job 2 -->
        <div class="flex flex-col md:flex-row gap-6">
            <p class="text-sky-400 font-semibold w-full md:w-1/4">2018 - 2021</p>
            <div class="w-full md:w-3/4">
                <h3 class="text-xl font-bold text-white">Embedded Systems Engineer en Hardware Innovators</h3>
                <p class="mt-2 text-slate-400">Desarrollé firmware en C/C++ para dispositivos de monitoreo industrial. Implementé protocolos de comunicación como MQTT y CoAP para la transmisión de datos a la nube.</p>
            </div>
        </div>
    </div>
</section>