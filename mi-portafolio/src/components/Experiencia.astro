<!-- SECTION: Experience -->
<section id="experiencia" class="py-16">
    <h2 class="text-3xl font-bold text-white mb-8">Experiencia Profesional</h2>
    <div class="space-y-12">
        <!-- Job 1 -->
        <div class="flex flex-col md:flex-row gap-6">
            <p class="text-sky-400 font-semibold w-full md:w-1/4">2021 - Presente</p>
            <div class="w-full md:w-3/4">
                <h3 class="text-xl font-bold text-white">Desarrollador de Software en Mundos Virtuales SPA.</h3>
                <p class="mt-2 text-slate-400">
Desarrollador de softwareDesarrollador de software
Mundos Virtuales · Jornada completaMundos Virtuales · Jornada completa sept. 2021 - actualidad · 3 años 11 mesessept. 2021 - actualidad · 3 años 11 meses Santiago, Región Metropolitana de Santiago, Chile · HíbridoSantiago, Región Metropolitana de Santiago, Chile · Híbrido

        Ingeniero de software full-stack con experiencia en el ciclo de vida completo de desarrollo, desde el diseño de hardware y la programación de bajo nivel hasta la arquitectura de backend y el desarrollo de aplicaciones móviles nativas.

        Lideré la modernización de un sistema transaccional, evolucionándolo desde una app de escritorio (C# / Windows Forms) a una solución móvil nativa para terminales POS (PAX A8700). La nueva aplicación Android fue desarrollada con Kotlin y Jetpack Compose bajo una arquitectura MVVM para garantizar una clara separación de responsabilidades y alta testeabilidad.

        Implementé la comunicación directa con hardware especializado tanto en la aplicación de escritorio como en la app Android. Ambas soluciones se integraban y controlaban un validador de billetes G&D ProNote y una placa de control personalizada.

        Diseñé y programé una placa de control de sensores y actuadores a medida con Arduino y KiCad. Esta placa, controlada directamente por las aplicaciones, centralizaba la interacción con los componentes físicos del sistema, demostrando una capacidad integral de solución de problemas (software-hardware).

        Arquitecté y construí la API RESTful de soporte con ASP.NET 8, aplicando principios de Clean Architecture y Domain-Driven Design (DDD) para crear un backend robusto, escalable y fácil de mantener.

        Gestioné la persistencia de datos con PostgreSQL y Entity Framework Core, asegurando la fiabilidad del backend a través de una estrategia de pruebas unitarias con xUnit.</p>
            </div>
        </div>
        <!-- Job 2 -->
        <div class="flex flex-col md:flex-row gap-6">
            <p class="text-sky-400 font-semibold w-full md:w-1/4">2018 - 2021</p>
            <div class="w-full md:w-3/4">
                <h3 class="text-xl font-bold text-white">Embedded Systems Engineer en Hardware Innovators</h3>
                <p class="mt-2 text-slate-400">Desarrollé firmware en C/C++ para dispositivos de monitoreo industrial. Implementé protocolos de comunicación como MQTT y CoAP para la transmisión de datos a la nube.</p>
            </div>
        </div>
    </div>
</section>