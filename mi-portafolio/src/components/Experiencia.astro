<!-- SECTION: Experience -->
<section id="experiencia" class="py-16">
    <h2 class="text-3xl font-bold text-white mb-8">Experiencia Profesional</h2>
    <div class="space-y-12">
        <!-- Job 1 -->
        <div class="flex flex-col md:flex-row gap-6">
            <p class="text-sky-400 font-semibold w-full md:w-1/4">2021 - Presente</p>
            <div class="w-full md:w-3/4">
                <h3 class="text-xl font-bold text-white">Desarrollador de Software Full-Stack | Mundos Virtuales SPA</h3>
                <p class="mt-2 text-slate-400">
                    Ingeniero full-stack especializado en el desarrollo completo de soluciones software-hardware. Lideré la modernización de un sistema transaccional desde una aplicación de escritorio (C# / Windows Forms) hacia una solución móvil nativa para terminales POS (PAX A8700).
                </p>
                <div class="mt-4">
                    <h4 class="text-lg font-semibold text-white mb-2">Logros principales:</h4>
                    <ul class="text-slate-400 space-y-2 list-disc list-inside">
                        <li>Desarrollé desde cero sistema completo de conteo de billetes con interfaz Windows Forms (C#) incluyendo autenticación, dashboard, estadísticas y pantalla de conteo principal</li>
                        <li>Integré hardware especializado implementando comunicación serial con validadora de billetes para procesamiento automático de transacciones</li>
                        <li>Diseñé y desarrollé placa de control personalizada con Arduino y KiCad para centralizar control de actuadores y monitoreo de sensores en tiempo real</li>
                        <li>Implementé sistema de control automatizado donde sensores activos disparan apertura de shutter via actuador, permitiendo flujo controlado de billetes</li>
                        <li>Arquitecté backend escalable con ASP.NET Core 8 y API RESTful para persistencia y gestión de datos transaccionales</li>
                        <li>Migré solución completa a Android nativo manteniendo toda la funcionalidad hardware-software en plataforma móvil</li>
                        <li>Automaticé soporte técnico regional con plataforma n8n integrando WhatsApp/Telegram para que técnicos consulten IA especializada con RAG sobre manuales Glory UWF</li>
                        <li>Modernicé aplicación legacy refactorizando código Python con PyQt6 para integración con contadora Glory UWF via red</li>
                    </ul>
                </div>
                <div class="mt-4">
                    <p class="text-sm text-sky-300"><strong>Stack técnico:</strong> C#, Kotlin, Python, PyQt6, Android, ASP.NET 8, PostgreSQL, Arduino, KiCad, Jetpack Compose, n8n, RAG, IA/LLM</p>
                </div>
            </div>
        </div>
        <!-- Job 2 -->
        <div class="flex flex-col md:flex-row gap-6">
            <p class="text-sky-400 font-semibold w-full md:w-1/4">2018 - 2021</p>
            <div class="w-full md:w-3/4">
                <h3 class="text-xl font-bold text-white">Embedded Systems Engineer en Permaquim SPA</h3>
                <p class="mt-2 text-slate-400">
                    Optimicé y refactoricé el firmware de bajo nivel escrito en Lenguaje C para microcontroladores PIC (PIC18F4520), utilizando el entorno de desarrollo MPLAB.
                </p>
                <p class="mt-2 text-slate-400">
                    El software gestiona el control y lectura de los sensores en una máquina depositaria (modelo P600), incluyendo la apertura de la bóveda y el mecanismo shutter para el paso de billetes.
                </p>
                <p class="mt-2 text-slate-400">
                    Mi trabajo se enfocó en mejorar la fiabilidad, el rendimiento y la mantenibilidad del código existente, asegurando una comunicación robusta con el PC y una lectura precisa de los estados de los sensores.
                </p>
            </div>
        </div>

        <!-- Job 3 -->
        <div class="flex flex-col md:flex-row gap-6">
            <p class="text-sky-400 font-semibold w-full md:w-1/4">2014 - 2016</p>
            <div class="w-full md:w-3/4">
                <h3 class="text-xl font-bold text-white">Service Desk Analyst | TMH International Group</h3>
                <p class="mt-2 text-slate-400">
                    Administrador de sistemas y soporte técnico integral responsable de la infraestructura tecnológica empresarial. Gestioné servidores Windows Server y Linux, administré bases de datos y proporcioné soporte técnico completo a usuarios finales.
                </p>
                <div class="mt-4">
                    <h4 class="text-lg font-semibold text-white mb-2">Responsabilidades principales:</h4>
                    <ul class="text-slate-400 space-y-2 list-disc list-inside">
                        <li>Administración y mantenimiento de bases de datos del sistema empresarial</li>
                        <li>Gestión de sitios web corporativos mediante cPanel y administración de hosting</li>
                        <li>Instalación y configuración de equipos, software corporativo y creación de cuentas de correo</li>
                        <li>Administración de central telefónica VoIP utilizando Digium y Elastix</li>
                        <li>Operación y mantenimiento de servidores Windows Server (Active Directory, Exchange, Proxy)</li>
                        <li>Administración de sistemas Linux (CentOS), FreeBSD y dispositivos NAS</li>
                        <li>Soporte técnico integral: redes LAN, dispositivos de impresión, correo electrónico</li>
                        <li>Gestión de accesos y permisos de software de oficina para usuarios corporativos</li>
                    </ul>
                </div>
                <div class="mt-4">
                    <p class="text-sm text-sky-300"><strong>Tecnologías:</strong> Windows Server, Linux (CentOS), FreeBSD, Active Directory, Exchange Server, VoIP (Digium/Elastix), cPanel, NAS, Redes LAN</p>
                </div>
            </div>
        </div>
    </div>
</section>