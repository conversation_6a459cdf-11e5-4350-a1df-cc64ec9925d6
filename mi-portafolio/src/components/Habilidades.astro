<!-- SECTION: Skills & Metrics -->
<section id="habilidades" class="py-12 sm:py-16">
    <h2 class="text-2xl sm:text-3xl font-bold text-white mb-6 sm:mb-8 text-center md:text-left">Habilidades & Métricas</h2>
    
    <!-- Key Metrics -->
    <div class="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8 sm:mb-12">
        <div class="bg-slate-800 rounded-lg p-4 sm:p-6 text-center">
            <div class="text-2xl sm:text-3xl font-bold text-sky-400">5+</div>
            <div class="text-xs sm:text-sm text-slate-400 mt-1">Años de Experiencia</div>
        </div>
        <div class="bg-slate-800 rounded-lg p-4 sm:p-6 text-center">
            <div class="text-2xl sm:text-3xl font-bold text-green-400">15+</div>
            <div class="text-xs sm:text-sm text-slate-400 mt-1">Proyectos Completados</div>
        </div>
        <div class="bg-slate-800 rounded-lg p-4 sm:p-6 text-center">
            <div class="text-2xl sm:text-3xl font-bold text-purple-400">8+</div>
            <div class="text-xs sm:text-sm text-slate-400 mt-1">Tecnologías Dominadas</div>
        </div>
        <div class="bg-slate-800 rounded-lg p-4 sm:p-6 text-center">
            <div class="text-2xl sm:text-3xl font-bold text-orange-400">100%</div>
            <div class="text-xs sm:text-sm text-slate-400 mt-1">Proyectos Entregados</div>
        </div>
    </div>

    <!-- Stack Tecnológico -->
    <div class="bg-slate-800 rounded-lg p-6 sm:p-8">
        <h3 class="text-lg sm:text-xl font-bold text-white mb-6 text-center">Stack Tecnológico</h3>

        <!-- Technologies Grid -->
        <div class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-8 gap-4 sm:gap-6">
            <!-- C# -->
            <div class="flex flex-col items-center group">
                <div class="w-12 h-12 sm:w-16 sm:h-16 bg-purple-500/20 rounded-lg flex items-center justify-center mb-2 group-hover:bg-purple-500/30 transition-colors">
                    <svg class="w-6 h-6 sm:w-8 sm:h-8 text-purple-400" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M1.194 7.543v8.913c0 1.103.588 2.122 1.544 2.674l8.262 4.77c.956.552 2.133.552 3.089 0l8.262-4.77c.956-.552 1.544-1.571 1.544-2.674V7.543c0-1.103-.588-2.122-1.544-2.674L14.089.099c-.956-.552-2.133-.552-3.089 0L2.738 4.869C1.782 5.421 1.194 6.44 1.194 7.543zM12 3.632l7.263 4.19v8.357L12 20.368l-7.263-4.19V7.822L12 3.632z"/>
                        <path d="M9.101 15.16h-.74v-4.32h.74v4.32zm2.22 0h-.74v-4.32h.74v4.32zm2.22 0h-.74v-4.32h.74v4.32zm2.22 0h-.74v-4.32h.74v4.32z"/>
                    </svg>
                </div>
                <span class="text-xs sm:text-sm text-slate-300 text-center">C#</span>
            </div>

            <!-- Python -->
            <div class="flex flex-col items-center group">
                <div class="w-12 h-12 sm:w-16 sm:h-16 bg-blue-500/20 rounded-lg flex items-center justify-center mb-2 group-hover:bg-blue-500/30 transition-colors">
                    <svg class="w-6 h-6 sm:w-8 sm:h-8 text-blue-400" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M14.25.18l.9.2.73.26.59.3.45.32.34.34.25.34.16.33.1.3.04.26.02.2-.01.13V8.5l-.05.63-.13.55-.21.46-.26.38-.3.31-.33.25-.35.19-.35.14-.33.1-.3.07-.26.04-.21.02H8.77l-.69.05-.59.14-.5.22-.41.27-.33.32-.27.35-.2.36-.15.37-.1.35-.07.32-.04.27-.02.21v3.06H3.17l-.21-.03-.28-.07-.32-.12-.35-.18-.36-.26-.36-.36-.35-.46-.32-.59-.28-.73-.21-.88-.14-1.05-.05-1.23.06-1.22.16-1.04.24-.87.32-.71.36-.57.4-.44.42-.33.42-.24.4-.16.36-.1.32-.05.26-.02.21-.01h5.84l.69-.05.59-.14.5-.21.41-.28.33-.32.27-.35.2-.36.15-.36.1-.35.07-.32.04-.28.02-.21V6.07h2.09l.14.01zm-6.47 14.25l-.23.33-.08.41.08.41.23.34.33.22.41.09.41-.09.33-.22.23-.34.08-.41-.08-.41-.23-.33-.33-.22-.41-.09-.41.09-.33.22z"/>
                    </svg>
                </div>
                <span class="text-xs sm:text-sm text-slate-300 text-center">Python</span>
            </div>

            <!-- Kotlin -->
            <div class="flex flex-col items-center group">
                <div class="w-12 h-12 sm:w-16 sm:h-16 bg-orange-500/20 rounded-lg flex items-center justify-center mb-2 group-hover:bg-orange-500/30 transition-colors">
                    <svg class="w-6 h-6 sm:w-8 sm:h-8 text-orange-400" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M1.3 24l11.3-11.5L24 24zM0 0h12.5L0 12.5zM13.1 0L0 13.1v10.4L23.6 0z"/>
                    </svg>
                </div>
                <span class="text-xs sm:text-sm text-slate-300 text-center">Kotlin</span>
            </div>

            <!-- C++ -->
            <div class="flex flex-col items-center group">
                <div class="w-12 h-12 sm:w-16 sm:h-16 bg-blue-600/20 rounded-lg flex items-center justify-center mb-2 group-hover:bg-blue-600/30 transition-colors">
                    <svg class="w-6 h-6 sm:w-8 sm:h-8 text-blue-500" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M22.394 6c-.167-.29-.398-.543-.652-.69L12.926.22c-.509-.294-1.34-.294-1.848 0L2.26 5.31c-.508.293-.923 1.013-.923 1.6v10.18c0 .294.104.62.271.91.167.29.398.543.652.69l8.816 5.09c.508.293 1.34.293 1.848 0l8.816-5.09c.254-.147.485-.4.652-.69.167-.29.27-.616.27-.91V6.91c.003-.294-.1-.62-.268-.91zM12 19.11c-3.92 0-7.109-3.19-7.109-7.11 0-3.92 3.19-7.11 7.109-7.11a7.133 7.133 0 016.156 3.553l-3.076 1.78a3.567 3.567 0 00-3.08-1.78A3.56 3.56 0 008.444 12 3.56 3.56 0 0012 15.555a3.57 3.57 0 003.08-1.778l3.078 1.78A7.135 7.135 0 0112 19.11zm7.11-6.715h-.79V11.61h-.79v.785h-.79v.79h.79v.785h.79v-.785h.79zm2.962 0h-.79V11.61h-.79v.785h-.79v.79h.79v.785h.79v-.785h.79z"/>
                    </svg>
                </div>
                <span class="text-xs sm:text-sm text-slate-300 text-center">C++</span>
            </div>

            <!-- PostgreSQL -->
            <div class="flex flex-col items-center group">
                <div class="w-12 h-12 sm:w-16 sm:h-16 bg-blue-700/20 rounded-lg flex items-center justify-center mb-2 group-hover:bg-blue-700/30 transition-colors">
                    <svg class="w-6 h-6 sm:w-8 sm:h-8 text-blue-300" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M23.111 5.441c-.777-.438-2.667-.248-4.778.653-.863.369-1.814.832-2.734 1.296-2.157-.15-3.833.063-4.778.653-.945.59-1.407 1.407-1.407 2.352 0 .945.462 1.762 1.407 2.352.945.59 2.621.803 4.778.653.92.464 1.871.927 2.734 1.296 2.111.901 4.001 1.091 4.778.653.777-.438.777-1.814 0-3.704-.369-.9-.832-1.851-1.296-2.734.464-.883.927-1.834 1.296-2.734.777-1.89.777-3.266 0-3.704z"/>
                    </svg>
                </div>
                <span class="text-xs sm:text-sm text-slate-300 text-center">PostgreSQL</span>
            </div>

            <!-- Android -->
            <div class="flex flex-col items-center group">
                <div class="w-12 h-12 sm:w-16 sm:h-16 bg-green-500/20 rounded-lg flex items-center justify-center mb-2 group-hover:bg-green-500/30 transition-colors">
                    <svg class="w-6 h-6 sm:w-8 sm:h-8 text-green-400" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17.523 15.3414c-.5511 0-.9993-.4486-.9993-.9997s.4482-.9993.9993-.9993c.5511 0 .9993.4482.9993.9993.0001.5511-.4482.9997-.9993.9997m-11.046 0c-.5511 0-.9993-.4486-.9993-.9997s.4482-.9993.9993-.9993c.5511 0 .9993.4482.9993.9993 0 .5511-.4482.9997-.9993.9997m11.4045-6.02l1.9973-3.4592a.416.416 0 00-.1518-.5972.416.416 0 00-.5972.1518l-2.0223 3.5046C15.5027 8.2006 13.8386 7.9141 12 7.9141c-1.8386 0-3.5027.2865-4.8848.8493L5.0929 5.2588a.416.416 0 00-.5972-.1518.416.416 0 00-.1518.5972L6.3412 9.3214C4.8306 10.2849 3.9141 11.6675 3.9141 13.2v7.7859h16.1718V13.2c0-1.5325-.9165-2.9151-2.4271-3.8786"/>
                    </svg>
                </div>
                <span class="text-xs sm:text-sm text-slate-300 text-center">Android</span>
            </div>

            <!-- Arduino -->
            <div class="flex flex-col items-center group">
                <div class="w-12 h-12 sm:w-16 sm:h-16 bg-teal-500/20 rounded-lg flex items-center justify-center mb-2 group-hover:bg-teal-500/30 transition-colors">
                    <svg class="w-6 h-6 sm:w-8 sm:h-8 text-teal-400" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M23.82 12c0 6.518-5.302 11.82-11.82 11.82S.18 18.518.18 12 5.482.18 12 .18 23.82 5.482 23.82 12zm-3.54 0c0-4.568-3.712-8.28-8.28-8.28S3.72 7.432 3.72 12s3.712 8.28 8.28 8.28 8.28-3.712 8.28-8.28zM10.44 7.56H8.88v1.56H7.32v1.56h1.56v1.56h1.56V10.68h1.56V9.12h-1.56V7.56zm5.04 3.12h-3.12v1.56h3.12v-1.56z"/>
                    </svg>
                </div>
                <span class="text-xs sm:text-sm text-slate-300 text-center">Arduino</span>
            </div>

            <!-- ASP.NET -->
            <div class="flex flex-col items-center group">
                <div class="w-12 h-12 sm:w-16 sm:h-16 bg-indigo-500/20 rounded-lg flex items-center justify-center mb-2 group-hover:bg-indigo-500/30 transition-colors">
                    <svg class="w-6 h-6 sm:w-8 sm:h-8 text-indigo-400" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M1.194 7.543v8.913c0 1.103.588 2.122 1.544 2.674l8.262 4.77c.956.552 2.133.552 3.089 0l8.262-4.77c.956-.552 1.544-1.571 1.544-2.674V7.543c0-1.103-.588-2.122-1.544-2.674L14.089.099c-.956-.552-2.133-.552-3.089 0L2.738 4.869C1.782 5.421 1.194 6.44 1.194 7.543z"/>
                    </svg>
                </div>
                <span class="text-xs sm:text-sm text-slate-300 text-center">ASP.NET</span>
            </div>

            <!-- Docker -->
            <div class="flex flex-col items-center group">
                <div class="w-12 h-12 sm:w-16 sm:h-16 bg-blue-400/20 rounded-lg flex items-center justify-center mb-2 group-hover:bg-blue-400/30 transition-colors">
                    <svg class="w-6 h-6 sm:w-8 sm:h-8 text-blue-400" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M13.983 11.078h2.119a.186.186 0 00.186-.185V9.006a.186.186 0 00-.186-.186h-2.119a.185.185 0 00-.185.185v1.888c0 .102.083.185.185.185m-2.954-5.43h2.118a.186.186 0 00.186-.186V3.574a.186.186 0 00-.186-.185h-2.118a.185.185 0 00-.185.185v1.888c0 .102.082.185.185.186m0 2.716h2.118a.187.187 0 00.186-.186V6.29a.186.186 0 00-.186-.185h-2.118a.185.185 0 00-.185.185v1.887c0 .102.082.185.185.186m-2.93 0h2.12a.186.186 0 00.184-.186V6.29a.185.185 0 00-.185-.185H8.1a.185.185 0 00-.185.185v1.887c0 .102.083.185.185.186m-2.964 0h2.119a.186.186 0 00.185-.186V6.29a.185.185 0 00-.185-.185H5.136a.186.186 0 00-.186.185v1.887c0 .102.084.185.186.186m5.893 2.715h2.118a.186.186 0 00.186-.185V9.006a.186.186 0 00-.186-.186h-2.118a.185.185 0 00-.185.185v1.888c0 .102.082.185.185.185m-2.93 0h2.12a.185.185 0 00.184-.185V9.006a.185.185 0 00-.184-.186h-2.12a.185.185 0 00-.184.185v1.888c0 .102.083.185.185.185m-2.964 0h2.119a.185.185 0 00.185-.185V9.006a.185.185 0 00-.184-.186h-2.12a.186.186 0 00-.186.186v1.887c0 .102.084.185.186.185m-2.92 0h2.12a.185.185 0 00.184-.185V9.006a.185.185 0 00-.184-.186h-2.12a.185.185 0 00-.184.185v1.888c0 .102.082.185.185.185M23.763 9.89c-.065-.051-.672-.51-1.954-.51-.338 0-.676.03-1.01.087-.248-1.7-1.653-2.53-1.716-2.566l-.344-.199-.226.327c-.284.438-.49.922-.612 1.43-.23.97-.09 1.882.403 2.661-.595.332-1.55.413-1.744.42H.751a.751.751 0 00-.75.748 11.376 11.376 0 00.692 4.062c.545 1.428 1.355 2.48 2.41 3.124 1.18.723 3.1 1.137 5.275 1.137.983 0 1.97-.084 2.944-.25 1.32-.225 2.595-.62 3.78-1.177 1.8-.85 3.32-2.12 4.555-3.8.945.02 2.9.04 3.9-1.85.03-.067.067-.134.097-.2l.138-.33-.36-.202z"/>
                    </svg>
                </div>
                <span class="text-xs sm:text-sm text-slate-300 text-center">Docker</span>
            </div>

            <!-- Azure -->
            <div class="flex flex-col items-center group">
                <div class="w-12 h-12 sm:w-16 sm:h-16 bg-blue-600/20 rounded-lg flex items-center justify-center mb-2 group-hover:bg-blue-600/30 transition-colors">
                    <svg class="w-6 h-6 sm:w-8 sm:h-8 text-blue-500" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M5.483 21.3L12 24l6.517-2.7L24 18.6 12 12 0 18.6l5.483 2.7zM12 0L0 6l12 6L24 6 12 0z"/>
                    </svg>
                </div>
                <span class="text-xs sm:text-sm text-slate-300 text-center">Azure</span>
            </div>

            <!-- Raspberry Pi -->
            <div class="flex flex-col items-center group">
                <div class="w-12 h-12 sm:w-16 sm:h-16 bg-red-500/20 rounded-lg flex items-center justify-center mb-2 group-hover:bg-red-500/30 transition-colors">
                    <svg class="w-6 h-6 sm:w-8 sm:h-8 text-red-400" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M11.999 0C5.373 0 0 5.373 0 12s5.373 12 11.999 12C18.626 24 24 18.627 24 12S18.626 0 11.999 0zm6.761 5.43a10.24 10.24 0 012.165 3.391l-1.714.429c-.456-1.173-1.145-2.23-2.015-3.105l1.564-1.715zm-2.982.187l-1.564 1.715a8.15 8.15 0 00-2.214-.617V4.572c.789.068 1.554.238 2.278.545l1.5-1.5zm-4.778.545V4.572a8.15 8.15 0 00-2.214.617L7.222 3.474c.724-.307 1.489-.477 2.278-.545l1.5 1.5zm-2.278.545a8.15 8.15 0 00-2.015 3.105l-1.714-.429a10.24 10.24 0 012.165-3.391l1.564 1.715z"/>
                    </svg>
                </div>
                <span class="text-xs sm:text-sm text-slate-300 text-center">Raspberry Pi</span>
            </div>

            <!-- n8n -->
            <div class="flex flex-col items-center group">
                <div class="w-12 h-12 sm:w-16 sm:h-16 bg-pink-500/20 rounded-lg flex items-center justify-center mb-2 group-hover:bg-pink-500/30 transition-colors">
                    <svg class="w-6 h-6 sm:w-8 sm:h-8 text-pink-400" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm5.568 14.568a1.432 1.432 0 11-2.864 0 1.432 1.432 0 012.864 0zM12 17.432a1.432 1.432 0 110-2.864 1.432 1.432 0 010 2.864zm-5.568-2.864a1.432 1.432 0 110-2.864 1.432 1.432 0 010 2.864zM12 11.432a1.432 1.432 0 110-2.864 1.432 1.432 0 010 2.864zm5.568-2.864a1.432 1.432 0 110-2.864 1.432 1.432 0 010 2.864zM12 5.432a1.432 1.432 0 110-2.864 1.432 1.432 0 010 2.864zM6.432 8.568a1.432 1.432 0 110-2.864 1.432 1.432 0 010 2.864z"/>
                    </svg>
                </div>
                <span class="text-xs sm:text-sm text-slate-300 text-center">n8n</span>
            </div>

            <!-- Git -->
            <div class="flex flex-col items-center group">
                <div class="w-12 h-12 sm:w-16 sm:h-16 bg-orange-600/20 rounded-lg flex items-center justify-center mb-2 group-hover:bg-orange-600/30 transition-colors">
                    <svg class="w-6 h-6 sm:w-8 sm:h-8 text-orange-500" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M23.546 10.93L13.067.452c-.604-.603-1.582-.603-2.188 0L8.708 2.627l2.76 2.76c.645-.215 1.379-.07 1.889.441.516.515.658 1.258.438 1.9l2.658 2.66c.645-.223 1.387-.078 1.9.435.721.72.721 1.884 0 2.604-.719.719-1.881.719-2.6 0-.539-.541-.674-1.337-.404-1.996L12.86 8.955v6.525c.176.086.342.203.488.348.713.721.713 1.883 0 2.6-.719.721-1.889.721-2.609 0-.719-.719-.719-1.879 0-2.598.182-.18.387-.316.605-.406V8.835c-.217-.091-.424-.222-.6-.401-.545-.545-.676-1.342-.396-2.009L7.636 3.7.45 10.881c-.6.605-.6 1.584 0 2.189l10.48 10.477c.604.604 1.582.604 2.186 0l10.43-10.43c.605-.603.605-1.582 0-2.187"/>
                    </svg>
                </div>
                <span class="text-xs sm:text-sm text-slate-300 text-center">Git</span>
            </div>

            <!-- Linux -->
            <div class="flex flex-col items-center group">
                <div class="w-12 h-12 sm:w-16 sm:h-16 bg-yellow-600/20 rounded-lg flex items-center justify-center mb-2 group-hover:bg-yellow-600/30 transition-colors">
                    <svg class="w-6 h-6 sm:w-8 sm:h-8 text-yellow-500" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12.504 0c-.155 0-.315.008-.48.021-4.226.333-3.105 4.807-3.17 6.298-.076 1.092-.3 1.953-1.05 3.02-.885 1.051-2.127 2.75-2.716 4.521-.278.832-.41 1.684-.287 2.489a.424.424 0 00-.11.135c-.26.268-.45.6-.663.839-.199.199-.485.267-.797.4-.313.136-.658.269-.864.68-.09.189-.136.394-.132.602 0 .199.027.4.055.536.058.399.116.728.04.97-.249.68-.28 1.145-.106 1.484.174.334.535.47.94.601.81.2 1.91.135 2.774.6.926.466 1.866.67 2.616.47.526-.116.97-.464 1.208-.946.587-.003 1.23-.269 2.26-.334.699-.058 1.574.267 2.577.2.025.134.063.198.114.333l.003.003c.391.778 1.113 1.132 1.884 1.071.771-.06 1.592-.536 2.257-1.306.631-.765 1.683-1.084 2.378-1.503.348-.199.629-.469.649-.853.023-.4-.2-.811-.714-1.376v-.097l-.003-.003c-.17-.2-.25-.535-.338-.926-.085-.401-.182-.786-.492-1.046h-.003c-.059-.054-.123-.067-.188-.135a.357.357 0 00-.19-.064c.431-1.278.264-2.55-.173-3.694-.533-1.41-1.465-2.638-2.175-3.483-.796-1.005-1.576-1.957-1.56-3.368.026-2.152.236-6.133-3.544-6.139zm.529 3.405h.013c.213 0 .396.062.584.198.19.135.33.332.438.533.105.259.158.459.166.724 0-.02.006-.04.006-.06v.105a.086.086 0 01-.004-.021l-.004-.024a1.807 1.807 0 01-.15.706.953.953 0 01-.213.335.71.71 0 00-.088-.042c-.104-.045-.198-.064-.284-.133a1.312 1.312 0 00-.22-.066c-.05-.006-.104-.005-.159-.004l-.05.004c-.14.301-.276.65-.456.962a1.819 1.819 0 01-.145.184c-.06.064-.123.1-.183.135.27-.464.424-1.061.424-1.748 0-.437-.1-.835-.257-1.158-.154-.324-.38-.54-.688-.540z"/>
                    </svg>
                </div>
                <span class="text-xs sm:text-sm text-slate-300 text-center">Linux</span>
            </div>

            <!-- KiCad -->
            <div class="flex flex-col items-center group">
                <div class="w-12 h-12 sm:w-16 sm:h-16 bg-cyan-500/20 rounded-lg flex items-center justify-center mb-2 group-hover:bg-cyan-500/30 transition-colors">
                    <svg class="w-6 h-6 sm:w-8 sm:h-8 text-cyan-400" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7.907 0C3.546 0 0 3.546 0 7.907s3.546 7.907 7.907 7.907 7.907-3.546 7.907-7.907S12.268 0 7.907 0zm8.186 8.186c-4.367 0-7.907 3.54-7.907 7.907S11.726 24 16.093 24 24 20.46 24 16.093s-3.54-7.907-7.907-7.907z"/>
                    </svg>
                </div>
                <span class="text-xs sm:text-sm text-slate-300 text-center">KiCad</span>
            </div>
        </div>
    </div>

    <!-- Unique Value Propositions -->
    <div class="mt-8 sm:mt-12">
        <h3 class="text-lg sm:text-xl font-bold text-white mb-6 text-center">¿Por qué elegirme?</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6">
            <div class="bg-gradient-to-br from-sky-900/30 to-sky-800/20 border border-sky-700/30 rounded-lg p-4 sm:p-6 text-center">
                <div class="text-3xl sm:text-4xl mb-3">🔧</div>
                <h4 class="text-base sm:text-lg font-bold text-white mb-2">Full-Stack Completo</h4>
                <p class="text-xs sm:text-sm text-slate-400">Desde firmware hasta frontend, manejo toda la pila tecnológica</p>
            </div>
            <div class="bg-gradient-to-br from-green-900/30 to-green-800/20 border border-green-700/30 rounded-lg p-4 sm:p-6 text-center">
                <div class="text-3xl sm:text-4xl mb-3">⚡</div>
                <h4 class="text-base sm:text-lg font-bold text-white mb-2">Entrega Rápida</h4>
                <p class="text-xs sm:text-sm text-slate-400">Metodologías ágiles y experiencia en proyectos críticos</p>
            </div>
            <div class="bg-gradient-to-br from-purple-900/30 to-purple-800/20 border border-purple-700/30 rounded-lg p-4 sm:p-6 text-center">
                <div class="text-3xl sm:text-4xl mb-3">🚀</div>
                <h4 class="text-base sm:text-lg font-bold text-white mb-2">Innovación Constante</h4>
                <p class="text-xs sm:text-sm text-slate-400">Siempre actualizado con las últimas tecnologías</p>
            </div>
        </div>
    </div>
</section>
