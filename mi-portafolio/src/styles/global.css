@import "tailwindcss";

/* Mobile-first responsive improvements */
@media (max-width: 640px) {
  /* Improve touch targets */
  .toggle-experience {
    min-height: 44px;
    padding: 12px 16px;
  }

  /* Better spacing for mobile */
  .experience-content ul {
    padding-left: 1rem;
  }

  /* Improve readability on small screens */
  .experience-content li {
    margin-bottom: 0.75rem;
  }

  /* Better button spacing */
  .hero-buttons {
    gap: 0.75rem;
  }
}

/* Smooth scrolling for navigation */
html {
  scroll-behavior: smooth;
}

/* Focus states for accessibility */
a:focus,
button:focus {
  outline: 2px solid #0ea5e9;
  outline-offset: 2px;
}

/* Improve text selection */
::selection {
  background-color: #0ea5e9;
  color: white;
}

/* Profile image enhancements */
.profile-image {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.profile-image:hover {
  transform: scale(1.05);
  box-shadow: 0 20px 40px rgba(14, 165, 233, 0.3);
}

/* Animated gradient border for profile */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.profile-border::before {
  content: '';
  position: absolute;
  inset: -2px;
  padding: 2px;
  background: linear-gradient(45deg, #0ea5e9, #8b5cf6, #06b6d4, #0ea5e9);
  border-radius: 50%;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  animation: rotate 3s linear infinite;
}