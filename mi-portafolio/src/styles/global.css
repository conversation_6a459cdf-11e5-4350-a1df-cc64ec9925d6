@import "tailwindcss";

/* Mobile-first responsive improvements */
@media (max-width: 640px) {
  /* Improve touch targets */
  .toggle-experience {
    min-height: 44px;
    padding: 12px 16px;
  }

  /* Better spacing for mobile */
  .experience-content ul {
    padding-left: 1rem;
  }

  /* Improve readability on small screens */
  .experience-content li {
    margin-bottom: 0.75rem;
  }

  /* Better button spacing */
  .hero-buttons {
    gap: 0.75rem;
  }
}

/* Smooth scrolling for navigation */
html {
  scroll-behavior: smooth;
}

/* Focus states for accessibility */
a:focus,
button:focus {
  outline: 2px solid #0ea5e9;
  outline-offset: 2px;
}

/* Improve text selection */
::selection {
  background-color: #0ea5e9;
  color: white;
}