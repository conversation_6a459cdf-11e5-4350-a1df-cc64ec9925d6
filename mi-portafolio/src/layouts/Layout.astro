---
import '../styles/global.css';

interface Props {
	title: string;
}
const { title } = Astro.props;
---
<!DOCTYPE html>
<html lang="es" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> | Desarrollador Backend & Embebidos</title>
    
    <!-- Tailwind CSS via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700;900&display=swap" rel="stylesheet">

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide-react@latest/dist/lucide-react.js"></script>
    <script src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>

    <style>
        /* Custom styles to make Lucide Icons work with basic HTML */
        .lucide {
            display: inline-block;
            width: 1em;
            height: 1em;
            stroke-width: 2;
            stroke: currentColor;
            fill: none;
        }
        body {
            font-family: 'Inter', sans-serif;
        }
    </style>
</head>
<body class="bg-slate-900 text-slate-300">

    <!-- Main Container -->
    <div class="max-w-4xl mx-auto p-4 sm:p-8">

        <!-- Header & Navigation -->
        <header class="flex justify-between items-center py-6">
            <a href="#" class="text-xl font-bold text-white">Alexis Ocando</a>
            <nav class="hidden md:flex space-x-6 text-sm font-medium">
                <a href="#experiencia" class="hover:text-sky-400 transition-colors">Experiencia</a>
                <a href="#proyectos" class="hover:text-sky-400 transition-colors">Proyectos</a>
                <a href="#estudios" class="hover:text-sky-400 transition-colors">Estudios</a>
                <a href="#contacto" class="hover:text-sky-400 transition-colors">Contacto</a>
            </nav>
        </header>

        <main>
            <slot />

        </main>

        <!-- Footer -->
        <footer class="text-center py-8 border-t border-slate-800">
            <p class="text-sm text-slate-500">&copy; 2025 [Alexis Ocando]. Construido con Astro y Tailwind CSS.</p>
        </footer>

    </div>

</body>
</html>
