---
import '../styles/global.css';

interface Props {
	title: string;
}
const { title } = Astro.props;
---
<!DOCTYPE html>
<html lang="es" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    
    <!-- Tailwind CSS via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700;900&display=swap" rel="stylesheet">

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide-react@latest/dist/lucide-react.js"></script>
    <script src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>

    <style>
        /* Custom styles to make Lucide Icons work with basic HTML */
        .lucide {
            display: inline-block;
            width: 1em;
            height: 1em;
            stroke-width: 2;
            stroke: currentColor;
            fill: none;
        }
        body {
            font-family: 'Inter', sans-serif;
        }
    </style>
</head>
<body class="bg-slate-900 text-slate-300">

    <!-- Main Container -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">

        <!-- Header & Navigation -->
        <header class="flex flex-col sm:flex-row justify-between items-center py-4 sm:py-6 gap-4">
            <a href="#" class="text-xl sm:text-2xl font-bold text-white">Alexis Ocando</a>

            <!-- Mobile Navigation -->
            <nav class="flex flex-wrap justify-center sm:hidden gap-3 text-xs font-medium">
                <a href="#habilidades" class="hover:text-sky-400 transition-colors px-2 py-1">Habilidades</a>
                <a href="#experiencia" class="hover:text-sky-400 transition-colors px-2 py-1">Experiencia</a>
                <a href="#proyectos" class="hover:text-sky-400 transition-colors px-2 py-1">Proyectos</a>
                <a href="#contacto" class="hover:text-sky-400 transition-colors px-2 py-1">Contacto</a>
            </nav>

            <!-- Desktop Navigation -->
            <nav class="hidden sm:flex space-x-4 lg:space-x-6 text-sm font-medium">
                <a href="#habilidades" class="hover:text-sky-400 transition-colors">Habilidades</a>
                <a href="#experiencia" class="hover:text-sky-400 transition-colors">Experiencia</a>
                <a href="#logros" class="hover:text-sky-400 transition-colors">Logros</a>
                <a href="#proyectos" class="hover:text-sky-400 transition-colors">Proyectos</a>
                <a href="#testimonios" class="hover:text-sky-400 transition-colors">Testimonios</a>
                <a href="#contacto" class="hover:text-sky-400 transition-colors">Contacto</a>
            </nav>
        </header>

        <main>
            <slot />

        </main>

        <!-- Footer -->
        <footer class="text-center py-6 sm:py-8 border-t border-slate-800 px-4 sm:px-0">
            <p class="text-xs sm:text-sm text-slate-500">&copy; 2025 Alexis Ocando. Construido con Astro y Tailwind CSS.</p>
        </footer>

    </div>

    <!-- Enhanced Navigation Script -->
    <script>
        // Smooth scroll for navigation links
        document.addEventListener('DOMContentLoaded', () => {
            const navLinks = document.querySelectorAll('nav a[href^="#"]') as NodeListOf<HTMLAnchorElement>;

            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    const href = link.getAttribute('href');
                    if (href) {
                        const targetId = href.substring(1);
                        const targetElement = document.getElementById(targetId);

                        if (targetElement) {
                            const headerHeight = 80; // Account for fixed header if any
                            const targetPosition = targetElement.offsetTop - headerHeight;

                            window.scrollTo({
                                top: targetPosition,
                                behavior: 'smooth'
                            });
                        }
                    }
                });
            });

            // Add active state to navigation based on scroll position
            const sections = document.querySelectorAll('section[id]') as NodeListOf<HTMLElement>;
            const navItems = document.querySelectorAll('nav a[href^="#"]') as NodeListOf<HTMLAnchorElement>;

            function updateActiveNav() {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop - 100;
                    if (window.scrollY >= sectionTop) {
                        const id = section.getAttribute('id');
                        if (id) {
                            current = id;
                        }
                    }
                });

                navItems.forEach(item => {
                    item.classList.remove('text-sky-400');
                    const href = item.getAttribute('href');
                    if (href === `#${current}`) {
                        item.classList.add('text-sky-400');
                    }
                });
            }

            window.addEventListener('scroll', updateActiveNav);
            updateActiveNav(); // Initial call
        });
    </script>

</body>
</html>
